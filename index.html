<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Capsules Animated Sticky Cards | Codegrid</title>
    <link rel="stylesheet" href="styles.css" />
</head>
<body>
    <section class="intro">
        <h1>We design spaces that don’t just exist.</h1>
    </section>

    <section class="cards">
        <div class="card">
            <div class="card-marquee">
                <div class="marquee">
                    <h1>Design Beyond Boundaries</h1>
                    <h1>Built for Tomorrow</h1>
                    <h1>Real Impact</h1>
                    <h1>Digital Visions</h1>
                </div>
            </div>
            <div class="card-wrapper">
                <div class="card-content">
                    <div class="card-title">
                        <h1>Curved Horizon</h1>
                    </div>
                    <div class="card-description">
                        <p>
                            A futuristic residence that plays with curvature and flow,
                            blending bold geometry with natural topography.
                        </p>
                    </div>
                </div>
                <div class="card-img">
                    <img src="img/card-img-1.jpg" alt="Curved Horizon" />
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-wrapper">
                <div class="card-content">
                    <div class="card-title">
                        <h1>Glass Haven</h1>
                    </div>
                    <div class="card-description">
                        <p>
                            A sleek pavilion of pure transparency, openness and light,
                            designed to dissolve into its environment.
                        </p>
                    </div>
                </div>
                <div class="card-img">
                    <img src="img/card-img-2.jpg" alt="Glass Haven" />
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-wrapper">
                <div class="card-content">
                    <div class="card-title">
                        <h1>Moss Cube</h1>
                    </div>
                    <div class="card-description">
                        <p>
                            A minimalist cube home crowned with a living moss dome,
                            merging micro-architecture with ecological design.
                        </p>
                    </div>
                </div>
                <div class="card-img">
                    <img src="img/card-img-3.jpg" alt="Moss Cube" />
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-wrapper">
                <div class="card-content">
                    <div class="card-title">
                        <h1>Floating Shelter</h1>
                    </div>
                    <div class="card-description">
                        <p>
                            This design explores an ethereal structure perched on a grassy
                            islet, seemingly hovering above water.
                        </p>
                    </div>
                </div>
                <div class="card-img">
                    <img src="img/card-img-4.jpg" alt="Floating Shelter" />
                </div>
            </div>
        </div>
    </section>

    <section class="outro">
        <h1>Architecture reimagined for the virtual age.</h1>
    </section>

    <!-- GSAP Core, ScrollTrigger & Lenis via CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://unpkg.com/@studio-freight/lenis@1.0.34/dist/lenis.min.js"></script>

    <script src="marquee.js"></script>
    <script src="script.js"></script>
</body>
</html>