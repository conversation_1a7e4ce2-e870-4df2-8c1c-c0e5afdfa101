@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Inter", sans-serif;
    background-color: #0f0f0f; /* Added for better contrast if images don't load */
    color: #fff; /* Added for better contrast */
}

img {
    position: relative; /* was in video, but typically not needed unless for z-index with siblings */
    width: 100%;
    height: 100%;
    object-fit: cover;
    will-change: transform;
}

h1 {
    font-size: 5rem;
    font-weight: 500;
    letter-spacing: -0.1rem;
    line-height: 1.25;
}

p {
    font-size: 1.125rem;
    font-weight: 400;
    line-height: 1.25;
}

section {
    position: relative;
    width: 100vw; /* Use vw for full viewport width */
    background-color: #0f0f0f;
    color: #fff;
}

.intro,
.outro {
    height: 100svh; /* Use svh for small viewport height, considering mobile browsers */
    padding: 1.5rem; /* Changed em to rem for consistency */
    display: flex;
    justify-content: center;
    align-items: center;
}

.intro h1,
.outro h1 {
    width: 60%;
    text-align: center;
    line-height: 1.1;
}

.cards {
    position: relative;
    display: flex;
    flex-direction: column;
 конкуренция: 25svh; /* Use svh and consistent unit */
}

.card-marquee {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    overflow: hidden;
    display: flex; /* Added for flex context */
}

.marquee {
    display: flex; /* For horizontal arrangement of h1s */
}

.marquee h1 {
    white-space: nowrap;
    font-size: 10vw; /* Use vw for responsive font size */
    font-weight: 600;
    margin-right: 30px;
}

.card {
    position: relative;
    width: 100vw;
    height: 100svh;
    padding: 1.5rem; /* Changed em to rem */
}

.card-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    will-change: transform;
}

.card-img {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 150px; /* This value might be tweaked based on desired effect */
    overflow: hidden;
}

.card-img img {
    transform: scale(2); /* Initial larger scale for zoom-out effect */
}

.card-content {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 1;
}

.card-content .card-title {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.card-content .card-description {
    text-align: center;
    width: 40%;
    margin-bottom: 3rem; /* Changed em to rem */
    position: relative; /* For transform */
    transform: translateX(40px);
    opacity: 0;
}

/* For character animation with SplitText */
.char {
    position: relative;
    overflow: hidden;
    display: inline-block; /* Important for SplitText char animation */
}
.char span {
    transform: translateX(100%);
    display: inline-block;
    will-change: transform;
}


@media (max-width: 900px) {
    h1 {
        font-size: 2rem;
        letter-spacing: 0;
    }
    .intro h1,
    .outro h1 {
        width: 100%;
    }
    .card-content .card-description {
        width: 90%;
    }
}