// Using CDN-loaded libraries, no imports needed
// Libraries are loaded via script tags in HTML: gsap, ScrollTrigger, SplitText, Lenis

document.addEventListener("DOMContentLoaded", () => {
    // Register GSAP plugins (removed SplitText as it's not available on free CDN)
    gsap.registerPlugin(ScrollTrigger);

    console.log("GSAP and ScrollTrigger registered");
    console.log("ScrollTrigger version:", ScrollTrigger.version);

    // Simple test - scale the first card image on scroll
    const firstCard = document.querySelector(".card");
    const firstCardWrapper = firstCard.querySelector(".card-wrapper");
    const firstCardImg = firstCard.querySelector(".card-img img");

    console.log("Test elements:", { firstCard, firstCardWrapper, firstCardImg });

    if (firstCardWrapper && firstCardImg) {
        // Set initial state
        gsap.set(firstCardWrapper, { scale: 0.5, borderRadius: "400px" });
        gsap.set(firstCardImg, { scale: 1.5 });

        ScrollTrigger.create({
            trigger: firstCard,
            start: "top top",
            end: "+=200vh",
            scrub: true,
            pin: true,
            onUpdate: (self) => {
                const progress = self.progress;
                const wrapperScale = 0.5 + progress * 0.5; // 0.5 to 1
                const imgScale = 1.5 - progress * 0.5; // 1.5 to 1
                const borderRadius = 400 - progress * 375; // 400 to 25

                console.log("Scaling - progress:", progress, "wrapperScale:", wrapperScale, "imgScale:", imgScale);

                gsap.set(firstCardWrapper, {
                    scale: wrapperScale,
                    borderRadius: borderRadius + "px"
                });
                gsap.set(firstCardImg, { scale: imgScale });
            }
        });
    } else {
        console.error("Could not find required elements for scaling animation");
    }

    // Lenis smooth scroll setup - temporarily disabled for debugging
    // const lenis = new window.Lenis();
    // lenis.on("scroll", ScrollTrigger.update);
    // gsap.ticker.add((time) => {
    //     lenis.raf(time * 1000);
    // });
    // gsap.ticker.lagSmoothing(0);

    // Marquee animation for the first card
    window.setupMarqueeAnimation(); // Call the function from marquee.js

    const cards = gsap.utils.toArray(".card");
    const introCard = cards[0];

    // Setup main animations without SplitText
    setupMainAnimations();
});

function setupMainAnimations() {
    const cards = gsap.utils.toArray(".card");
    const introCard = cards[0];

    // Select elements for the intro card
    const introCardImgWrapper = introCard.querySelector(".card-wrapper");
    const introCardImg = introCard.querySelector(".card-img img");
    const introCardMarquee = introCard.querySelector(".card-marquee .marquee");
    const introCardTitle = introCard.querySelector(".card-title h1");
    const introCardDescription = introCard.querySelector(".card-description");

    console.log("Elements found:");
    console.log("- introCardImgWrapper:", introCardImgWrapper);
    console.log("- introCardImg:", introCardImg);
    console.log("- introCardMarquee:", introCardMarquee);
    console.log("- introCardTitle:", introCardTitle);
    console.log("- introCardDescription:", introCardDescription);

    // Initial state for intro card
    gsap.set(introCardImgWrapper, { scale: 0.5, borderRadius: "400px" });
    gsap.set(introCardImg, { scale: 1.5 }); // Initial zoom for parallax

    // Set initial state for text content (hidden)
    gsap.set(introCardTitle, { x: "100%", opacity: 0 });
    gsap.set(introCardDescription, { x: "40px", opacity: 0 });

    // Utility functions for content animation (simplified without SplitText)
    function animateContentIn(title, description) {
        gsap.to(title, {
            x: "0%",
            opacity: 1,
            duration: 0.75,
            ease: "power4.out",
        });
        gsap.to(description, {
            x: "0%",
            opacity: 1,
            duration: 0.75,
            delay: 0.1, // Slight delay after title
            ease: "power4.out",
        });
    }

    function animateContentOut(title, description) {
        gsap.to(title, {
            x: "100%", // Slide out to the right
            opacity: 0,
            duration: 0.5,
            ease: "power4.out",
        });
        gsap.to(description, {
            x: "40px", // Slide out to the right
            opacity: 0,
            duration: 0.5,
            ease: "power4.out",
        });
    }

    // --- Intro Card Animation ---
    let introCardContentRevealed = false;

    console.log("Setting up ScrollTrigger for intro card...");
    console.log("Intro card wrapper:", introCardImgWrapper);
    console.log("Intro card img:", introCardImg);

    // First ScrollTrigger: Pin the card smoothly
    ScrollTrigger.create({
        trigger: introCard,
        start: "top top",
        end: "+=300vh",
        pin: true,
        pinSpacing: true,
    });

    // Second ScrollTrigger: Handle the scaling animation
    ScrollTrigger.create({
        trigger: introCard,
        start: "top top",
        end: "+=300vh",
        scrub: 1,
        onUpdate: (self) => {
            const progress = self.progress;
            const imgScale = 0.5 + progress * 0.5; // Scale from 0.5 to 1
            const borderRadius = 400 - progress * 375; // Radius from 400 to 25
            const innerImgScale = 1.5 - progress * 0.5; // Scale from 1.5 down to 1 for parallax

            console.log("ScrollTrigger progress:", progress, "imgScale:", imgScale, "innerImgScale:", innerImgScale);

            gsap.set(introCardImgWrapper, {
                scale: imgScale,
                borderRadius: borderRadius + "px",
            });
            gsap.set(introCardImg, { scale: innerImgScale });

            // Fade out marquee
            if (imgScale >= 0.5 && imgScale <= 0.75) {
                const fadeProgress = (imgScale - 0.5) / (0.75 - 0.5);
                gsap.set(introCardMarquee, { opacity: 1 - fadeProgress });
            } else if (imgScale < 0.5) {
                gsap.set(introCardMarquee, { opacity: 1 });
            } else if (imgScale > 0.75) {
                gsap.set(introCardMarquee, { opacity: 0 });
            }

            // Reveal content when card is fully scaled
            if (progress >= 1 && !introCardContentRevealed) {
                introCardContentRevealed = true;
                animateContentIn(introCardTitle, introCardDescription);
            } else if (progress < 1 && introCardContentRevealed) {
                introCardContentRevealed = false;
                animateContentOut(introCardTitle, introCardDescription);
            }
        },
    });

    // --- Subsequent Cards Animation ---
    cards.forEach((card, index) => {
        if (index === 0) return; // Skip intro card as it has its own ScrollTrigger

        const cardTitle = card.querySelector(".card-title h1");
        const cardDescription = card.querySelector(".card-description");

        // Set initial state for text content (hidden)
        gsap.set(cardTitle, { x: "100%", opacity: 0 });
        gsap.set(cardDescription, { x: "40px", opacity: 0 });

        // Pin each card
        ScrollTrigger.create({
            trigger: card,
            start: "top top",
            end: "+=100vh", // Pin for the height of the viewport
            pin: true,
            pinSpacing: index === cards.length - 1 ? true : false, // Add spacing only for the last card's pin
            onEnter: () => animateContentIn(cardTitle, cardDescription),
            onLeaveBack: () => animateContentOut(cardTitle, cardDescription),
        });

        // Animate previous card out (scale down and fade)
        if (index < cards.length) { // Condition corrected: index > 0 is already true here
             ScrollTrigger.create({
                trigger: cards[index], // Trigger when the current card (next card) starts coming in
                start: "top bottom", // When top of current card hits bottom of viewport
                end: "top top", // When top of current card hits top of viewport
                scrub: true,
                onUpdate: (self) => {
                    const progress = self.progress;
                    const prevCardWrapper = cards[index - 1].querySelector(".card-wrapper");
                    gsap.set(prevCardWrapper, {
                        scale: 1 - progress * 0.25, // Scale down from 1 to 0.75
                        opacity: 1 - progress,      // Fade out from 1 to 0
                    });
                },
            });
        }
    });
}