import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";
import SplitText from "gsap/SplitText";
import Lenis from "lenis"; // Corrected from 'lenis' to 'Lenis' for constructor
import { setupMarqueeAnimation } from "./marquee.js";

document.addEventListener("DOMContentLoaded", () => {
    gsap.registerPlugin(ScrollTrigger, SplitText);

    // Lenis smooth scroll setup
    const lenis = new Lenis();
    lenis.on("scroll", ScrollTrigger.update);
    gsap.ticker.add((time) => {
        lenis.raf(time * 1000);
    });
    gsap.ticker.lagSmoothing(0);

    // Marquee animation for the first card
    setupMarqueeAnimation(); // Call the function from marquee.js

    const cards = gsap.utils.toArray(".card");
    const introCard = cards[0];

    const titles = gsap.utils.toArray(".card-title h1");
    titles.forEach((title) => {
        new SplitText(title, {
            type: "char",
            charsClass: "char",
            tag: "div", // Wrap each char in a div
        });
        // Then wrap the inner text of each char in a span
        gsap.utils.toArray(title.querySelectorAll(".char")).forEach(charDiv => {
            charDiv.innerHTML = `<span>${charDiv.textContent}</span>`;
        });
    });


    // Select elements for the intro card
    const introCardImgWrapper = introCard.querySelector(".card-wrapper");
    const introCardImg = introCard.querySelector(".card-img img");
    const introCardMarquee = introCard.querySelector(".card-marquee .marquee");
    const introCardTitleChars = introCard.querySelectorAll(".card-title .char span");
    const introCardDescription = introCard.querySelector(".card-description");


    // Initial state for intro card
    gsap.set(introCardImgWrapper, { scale: 0.5, borderRadius: "400px" });
    gsap.set(introCardImg, { scale: 1.5 }); // Initial zoom for parallax
    
    // Utility functions for content animation
    function animateContentIn(titleChars, description) {
        gsap.to(titleChars, {
            x: "0%",
            duration: 0.75,
            ease: "power4.out",
            stagger: 0.03, // Added stagger for title chars
        });
        gsap.to(description, {
            x: "0%",
            opacity: 1,
            duration: 0.75,
            delay: 0.1, // Slight delay after title
            ease: "power4.out",
        });
    }

    function animateContentOut(titleChars, description) {
        gsap.to(titleChars, {
            x: "100%", // Slide out to the right
            duration: 0.5,
            ease: "power4.out",
            stagger: 0.02,
        });
        gsap.to(description, {
            x: "40px", // Slide out to the right
            opacity: 0,
            duration: 0.5,
            ease: "power4.out",
        });
    }

    // --- Intro Card Animation ---
    let introCardContentRevealed = false;
    ScrollTrigger.create({
        trigger: introCard,
        start: "top top",
        end: "+=300svh", // Scroll 3 times the viewport height
        scrub: true, // Smooth scrubbing
        pin: true, // Pin the introCard
        onUpdate: (self) => {
            const progress = self.progress;
            const imgScale = 0.5 + progress * 0.5; // Scale from 0.5 to 1
            const borderRadius = 400 - progress * 375; // Radius from 400 to 25 (or less, depends on original 150px)
            const innerImgScale = 1.5 - progress * 0.5; // Scale from 1.5 down to 1 for parallax

            gsap.set(introCardImgWrapper, {
                scale: imgScale,
                borderRadius: borderRadius + "px",
            });
            gsap.set(introCardImg, { scale: innerImgScale });

            // Fade out marquee
            if (imgScale >= 0.5 && imgScale <= 0.75) {
                const fadeProgress = (imgScale - 0.5) / (0.75 - 0.5);
                gsap.set(introCardMarquee, { opacity: 1 - fadeProgress });
            } else if (imgScale < 0.5) {
                gsap.set(introCardMarquee, { opacity: 1 });
            } else if (imgScale > 0.75) {
                gsap.set(introCardMarquee, { opacity: 0 });
            }

            // Reveal content when card is fully scaled
            if (progress >= 1 && !introCardContentRevealed) {
                introCardContentRevealed = true;
                animateContentIn(introCardTitleChars, introCardDescription);
            } else if (progress < 1 && introCardContentRevealed) {
                introCardContentRevealed = false;
                animateContentOut(introCardTitleChars, introCardDescription);
            }
        },
    });

    // --- Subsequent Cards Animation ---
    cards.forEach((card, index) => {
        if (index === 0) return; // Skip intro card as it has its own ScrollTrigger

        const cardWrapper = card.querySelector(".card-wrapper");
        const cardImg = card.querySelector(".card-img img");
        const cardTitleChars = card.querySelectorAll(".card-title .char span");
        const cardDescription = card.querySelector(".card-description");

        // Pin each card
        ScrollTrigger.create({
            trigger: card,
            start: "top top",
            end: "+=100svh", // Pin for the height of the viewport
            pin: true,
            pinSpacing: index === cards.length - 1 ? true : false, // Add spacing only for the last card's pin
            onEnter: () => animateContentIn(cardTitleChars, cardDescription),
            onLeaveBack: () => animateContentOut(cardTitleChars, cardDescription),
        });

        // Animate previous card out (scale down and fade)
        if (index < cards.length) { // Condition corrected: index > 0 is already true here
             ScrollTrigger.create({
                trigger: cards[index], // Trigger when the current card (next card) starts coming in
                start: "top bottom", // When top of current card hits bottom of viewport
                end: "top top", // When top of current card hits top of viewport
                scrub: true,
                onUpdate: (self) => {
                    const progress = self.progress;
                    const prevCardWrapper = cards[index - 1].querySelector(".card-wrapper");
                    gsap.set(prevCardWrapper, {
                        scale: 1 - progress * 0.25, // Scale down from 1 to 0.75
                        opacity: 1 - progress,      // Fade out from 1 to 0
                    });
                },
            });
        }
    });
});